#!/usr/bin/env python3

# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Simple test script for RoHand robot loading.

This script tests basic loading of the RoHand robot to verify the USD file
and configuration work correctly in IsaacLab.
"""

import argparse

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Simple RoHand robot test.")
parser.add_argument("--num_envs", type=int, default=1, help="Number of environments to spawn.")

# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import math

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sim import SimulationContext
from isaaclab.utils import configclass

# Import the robot configuration
from isaaclab_assets.robots.rohand_right import ROHAND_RIGHT_CFG


@configclass
class SimpleTestSceneCfg(InteractiveSceneCfg):
    """Configuration for the simple test scene."""

    # robot
    robot: Articulation = ROHAND_RIGHT_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")


def run_simulator(sim: SimulationContext, scene: InteractiveScene):
    """Run the simulator with basic robot testing."""

    # Define the simulation stepping
    sim_dt = sim.get_physics_dt()
    count = 0

    # Get the robot
    robot = scene["robot"]

    # Print joint information
    print(f"Robot joint names: {robot.joint_names}")
    print(f"Number of joints: {robot.num_joints}")

    # Find slider joint indices
    slider_joint_indices = []
    slider_joint_names = [
        "righthand_th_slider_link",  # thumb
        "righthand_if_slider_link",  # index
        "righthand_mf_slider_link",  # middle
        "righthand_rf_slider_link",  # ring
        "righthand_lf_slider_link",  # little
    ]

    for slider_name in slider_joint_names:
        for i, joint_name in enumerate(robot.joint_names):
            if slider_name in joint_name:
                slider_joint_indices.append(i)
                print(f"Found slider joint: {slider_name} at index {i}")
                break

    if not slider_joint_indices:
        print("ERROR: No slider joints found!")
        return

    print(f"Found {len(slider_joint_indices)} slider joints")

    # Simulation loop
    while simulation_app.is_running():
        # Reset if needed
        if count % 500 == 0:
            # reset counter
            count = 0
            # reset the scene entities
            root_state = robot.data.default_root_state.clone()
            root_state[:, :3] += scene.env_origins
            robot.write_root_state_to_sim(root_state)
            # set joint positions
            joint_pos, joint_vel = robot.data.default_joint_pos.clone(), robot.data.default_joint_vel.clone()
            robot.write_joint_state_to_sim(joint_pos, joint_vel)
            # clear internal buffers
            scene.reset()
            print("[INFO]: Resetting robot state...")

        # Test basic joint movement
        if count > 100:  # Wait for stabilization
            # Create simple sinusoidal motion for slider joints
            time_factor = count * sim_dt

            # Set slider joint positions
            joint_pos_targets = robot.data.joint_pos.clone()

            for i, slider_idx in enumerate(slider_joint_indices):
                if slider_idx < joint_pos_targets.shape[1]:
                    # Simple sinusoidal motion
                    frequency = 0.5
                    amplitude = 0.005

                    # Generate sinusoidal motion
                    target_pos = amplitude * math.sin(frequency * time_factor)

                    # Clamp to safe range
                    target_pos = max(-0.003, min(0.008, target_pos))

                    joint_pos_targets[:, slider_idx] = target_pos

            # Apply the joint positions
            robot.set_joint_position_target(joint_pos_targets)

        # Write data to sim
        scene.write_data_to_sim()
        # Perform step
        sim.step()
        # Increment counter
        count += 1
        # Update buffers
        scene.update(sim_dt)

        # Print debug information every 2 seconds
        if count % int(2.0 / sim_dt) == 0:
            print(f"[INFO] Step: {count}")
            if slider_joint_indices:
                print(f"[INFO] Slider joint positions: {robot.data.joint_pos[0, slider_joint_indices].tolist()}")

        # Exit after some time for testing
        if count > 1000:
            print("[INFO] Test completed successfully!")
            break


def main():
    """Main function."""
    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(device=args_cli.device)
    sim = SimulationContext(sim_cfg)

    # Set main camera
    sim.set_camera_view([2.5, 0.0, 4.0], [0.0, 0.0, 2.0])

    # Design scene
    scene_cfg = SimpleTestSceneCfg(num_envs=args_cli.num_envs, env_spacing=2.0)
    scene = InteractiveScene(scene_cfg)

    # Play the simulator
    sim.reset()

    # Now we are ready!
    print("[INFO]: Setup complete...")

    # Run the simulator
    run_simulator(sim, scene)


if __name__ == "__main__":
    # run the main function
    main()
    # close sim app
    simulation_app.close()
