#!/usr/bin/env python3

# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test script for RoHand joint linkage functionality.

This script tests the joint linkage system for the RoHand right hand robot
to ensure that the finger joints move correctly based on slider positions.
"""

import argparse
import torch

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test RoHand joint linkage functionality.")
parser.add_argument("--num_envs", type=int, default=1, help="Number of environments to spawn.")
parser.add_argument("--headless", action="store_true", default=False, help="Run in headless mode.")

# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import math
import time

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sim import SimulationContext
from isaaclab.utils import configclass

# Import the robot configuration
from isaaclab_assets.robots.rohand_right import ROHAND_RIGHT_CFG


@configclass
class RoHandTestSceneCfg(InteractiveSceneCfg):
    """Configuration for the RoHand test scene."""

    # ground plane
    ground = sim_utils.GroundPlaneCfg()

    # lights
    light = sim_utils.DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75))

    # robot
    robot: Articulation = ROHAND_RIGHT_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")


def run_simulator(sim: SimulationContext, scene: InteractiveScene):
    """Run the simulator with joint linkage testing."""
    
    # Define the simulation stepping
    sim_dt = sim.get_physics_dt()
    count = 0
    
    # Get the robot
    robot = scene["robot"]
    
    # Print joint information
    print(f"Robot joint names: {robot.joint_names}")
    print(f"Number of joints: {robot.num_joints}")
    
    # Find slider joint indices
    slider_joint_indices = []
    slider_joint_names = [
        "righthand_th_slider_link",  # thumb
        "righthand_if_slider_link",  # index
        "righthand_mf_slider_link",  # middle
        "righthand_rf_slider_link",  # ring
        "righthand_lf_slider_link",  # little
    ]
    
    for slider_name in slider_joint_names:
        for i, joint_name in enumerate(robot.joint_names):
            if slider_name in joint_name:
                slider_joint_indices.append(i)
                print(f"Found slider joint: {slider_name} at index {i}")
                break
    
    if not slider_joint_indices:
        print("ERROR: No slider joints found!")
        return
    
    # Simulation loop
    while simulation_app.is_running():
        # Reset if needed
        if count % 500 == 0:
            # reset counter
            count = 0
            # reset the scene entities
            # root state
            # we offset the root state by the origin since the states are written in simulation world frame
            # if this is not done, then the robots will be spawned at the (0, 0, 0) of the simulation world
            root_state = robot.data.default_root_state.clone()
            root_state[:, :3] += scene.env_origins
            robot.write_root_state_to_sim(root_state)
            # set joint positions with some noise
            joint_pos, joint_vel = robot.data.default_joint_pos.clone(), robot.data.default_joint_vel.clone()
            robot.write_joint_state_to_sim(joint_pos, joint_vel)
            # clear internal buffers
            scene.reset()
            print("[INFO]: Resetting robot state...")
        
        # Test joint linkage by moving slider joints
        if count > 100:  # Wait for stabilization
            # Create sinusoidal motion for slider joints
            time_factor = count * sim_dt
            
            # Set slider joint positions
            joint_pos_targets = robot.data.joint_pos.clone()
            
            for i, slider_idx in enumerate(slider_joint_indices):
                if slider_idx < joint_pos_targets.shape[1]:
                    # Different frequency for each finger
                    frequency = 0.5 + i * 0.1
                    amplitude = 0.005 + i * 0.002  # Different amplitude for each finger
                    
                    # Generate sinusoidal motion
                    target_pos = amplitude * math.sin(frequency * time_factor)
                    
                    # Clamp to safe range
                    if i == 0:  # thumb
                        target_pos = max(-0.003, min(0.008, target_pos))
                    else:  # other fingers
                        target_pos = max(-0.003, min(0.016, target_pos))
                    
                    joint_pos_targets[:, slider_idx] = target_pos
            
            # Apply the joint positions
            robot.set_joint_position_target(joint_pos_targets)
        
        # Write data to sim
        scene.write_data_to_sim()
        # Perform step
        sim.step()
        # Increment counter
        count += 1
        # Update buffers
        scene.update(sim_dt)
        
        # Print debug information every 2 seconds
        if count % int(2.0 / sim_dt) == 0:
            print(f"[INFO] Step: {count}")
            print(f"[INFO] Slider joint positions: {robot.data.joint_pos[0, slider_joint_indices].tolist()}")


def main():
    """Main function."""
    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(device=args_cli.device, use_gpu_pipeline=args_cli.use_gpu_pipeline)
    sim = SimulationContext(sim_cfg)
    
    # Set main camera
    sim.set_camera_view([2.5, 0.0, 4.0], [0.0, 0.0, 2.0])
    
    # Design scene
    scene_cfg = RoHandTestSceneCfg(num_envs=args_cli.num_envs, env_spacing=2.0)
    scene = InteractiveScene(scene_cfg)
    
    # Play the simulator
    sim.reset()
    
    # Now we are ready!
    print("[INFO]: Setup complete...")
    
    # Run the simulator
    run_simulator(sim, scene)


if __name__ == "__main__":
    # run the main function
    main()
    # close sim app
    simulation_app.close()
