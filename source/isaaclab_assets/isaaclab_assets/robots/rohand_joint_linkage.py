# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Joint linkage system for RoHand right hand robot.

This module provides the joint linkage functionality that was originally implemented
as a USD BehaviorScript. It calculates the joint angles for finger joints based on
the slider joint positions to simulate realistic finger movement.
"""

import math
import torch
from typing import Dict, List, Optional

import omni.isaac.core.utils.prims as prim_utils
from omni.isaac.core.utils.stage import get_current_stage
from pxr import Sdf


# Constants from the original script
MAX_ANGLES_URDF = 4
MAX_ANGLES_URDF_THUMB = 3
MAX_FINGERS = 6
NUM_FINGERS = 5
EXTRA_MOTORS = 1

THUMB_ID = 0
INDEX_FINGER_ID = 1
MIDDLE_FINGER_ID = 2
RING_FINGER_ID = 3
LITTLE_FINGER_ID = 4
THUMB_ROOT_ID = 5

JOINTS_NAME = [
    ['righthand_th_proximal_link', 'righthand_th_slider_link', 'righthand_th_distal_link'],
    ['righthand_if_slider_link', 'righthand_if_proximal_link', 'righthand_if_distal_link'],
    ['righthand_mf_slider_link', 'righthand_mf_proximal_link', 'righthand_mf_distal_link'],
    ['righthand_rf_slider_link', 'righthand_rf_proximal_link', 'righthand_rf_distal_link'],
    ['righthand_lf_slider_link', 'righthand_lf_proximal_link', 'righthand_lf_distal_link'],
    ['righthand_th_root_link']
]

PI = 3.141592653589793
HALF_PI = 1.570796326794896
ONE_HALF_PI = 4.71238898038469

# Parameters from the original script
ThumbParams = [
    11.48, 0.48, 10.98, 10.00, 0.5983, 43.78, 2.08, 0.2155, 52.52, 9.90, 11.30, 1.2976, 27.54, 51.30, 26.8, 0.4184, 13.31
]

FingerParams = [
    [12.70, 8.60, 19.63, 14.25, 0.5353, 0.7624, 0.8496, 35.92, 27.44, 8.63, 7.69, 34.14, 50.23],
    [13.17, 8.60, 19.63, 14.31, 0.5353, 0.7763, 0.8496, 41.01, 32.39, 8.63, 7.69, 39.48, 50.23],
    [13.00, 8.60, 19.63, 14.25, 0.5353, 0.7624, 0.8496, 35.92, 27.44, 8.63, 7.69, 34.14, 50.23],
    [13.15, 8.60, 19.63, 14.27, 0.5353, 0.7502, 0.8141, 31.93, 23.59, 8.63, 7.53, 30.85, 35.53]
]

OffsetAngleForURDF_Thumb = [0.4243102998823798, 2.8587571556405598, 1.5319419424521146]

OffsetAngleForURDF = [
    [2.932191219049254, 2.8069436083614603, 2.5070833024292147, 2.0524510416836774],
    [2.9149674382495734, 2.7765308802396014, 2.5671093077696816, 2.050822356942187],
    [2.92349523247357, 2.7806045952072487, 2.4744162317989837, 2.0228874728582893],
    [2.91807750665145, 2.7543096279771366, 2.4606460165701547, 1.9656409021066734]
]


def METER_TO_MILLIMETER(value):
    return value * 1000


def MAP(value, in_min, in_max, out_min, out_max):
    return (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min


def CLAMP(value, min_value, max_value):
    return max(min(value, max_value), min_value)


def ANGLE_COS(L_a, L_b, L_c):
    return math.acos((L_b * L_b + L_c * L_c - L_a * L_a) / (2 * L_b * L_c))


def ANGLE_SIN(angle_a, L_a, L_b):
    return math.asin((L_b * math.sin(angle_a)) / L_a)


def LENGTH_COS(angle, L_b, L_c):
    return math.sqrt(L_b * L_b + L_c * L_c - 2 * L_b * L_c * math.cos(angle))


def LENGTH_SIN(angle_a, L_a, angle_b):
    return ((math.sin(angle_b) * L_a) / math.sin(angle_a))


def SQRT(value):
    return math.sqrt(value)


def ATAN(value):
    return math.atan(value)


def THUMB_OffsetToAngle(offset_to_ref):
    """Calculate thumb joint angles from slider offset."""
    pos = METER_TO_MILLIMETER(offset_to_ref)
    
    Angle_Thumb = [0.0] * MAX_ANGLES_URDF_THUMB
    L_BP0 = ThumbParams[0]
    L_OP = ThumbParams[1]
    L_AB = ThumbParams[2]
    L_OA = ThumbParams[3]
    Angle_OAR = ThumbParams[4]
    L_CT0 = ThumbParams[5]
    L_OT = ThumbParams[6]
    Angle_EOQ = ThumbParams[7]
    L_OE = ThumbParams[8]
    L_CD = ThumbParams[9]
    L_ED = ThumbParams[10]
    Angle_DEF = ThumbParams[11]
    L_EF = ThumbParams[12]
    XE = ThumbParams[13]

    L_BP = L_BP0 + pos
    L_OB = SQRT(L_OP * L_OP + L_BP * L_BP)
    Angle_OBP = ATAN(L_OP / L_BP)
    Angle_AOB = ANGLE_COS(L_AB, L_OA, L_OB)
    Angle_AOQ = Angle_AOB - Angle_OBP
    Angle_QOS = Angle_AOQ - Angle_OAR

    L_CT = L_CT0 + pos
    L_OC = SQRT(L_CT * L_CT + L_OT * L_OT)
    Angle_OCT = ATAN(L_OT / L_CT)
    Angle_EOC = Angle_EOQ + Angle_OCT
    L_CE = LENGTH_COS(Angle_EOC, L_OE, L_OC)
    Angle_CED = ANGLE_COS(L_CD, L_CE, L_ED)
    Angle_CEF = Angle_CED + Angle_DEF
    L_CF = LENGTH_COS(Angle_CEF, L_CE, L_EF)
    Angle_ECF = ANGLE_COS(L_EF, L_CE, L_CF)
    Angle_ECU = HALF_PI - ATAN((XE - L_CT) / L_CE)
    Angle_FCU = Angle_ECU - Angle_ECF
    Angle_QCF = HALF_PI - Angle_FCU
    Angle_ECD = ANGLE_COS(L_ED, L_CD, L_CE)
    Angle_ECQ = Angle_QCF - Angle_ECF
    Angle_DCT = Angle_ECD + Angle_ECQ + HALF_PI

    Angle_OCD = Angle_DCT - Angle_OCT
    L_OD = LENGTH_COS(Angle_OCD, L_OC, L_CD)
    Angle_OED = ANGLE_COS(L_OD, L_OE, L_ED)

    Angle_Thumb[0] = Angle_QOS - OffsetAngleForURDF_Thumb[0]  # proximal
    Angle_Thumb[1] = Angle_DCT - OffsetAngleForURDF_Thumb[1]  # connecting
    Angle_Thumb[2] = OffsetAngleForURDF_Thumb[2] - Angle_OED  # distal

    return Angle_Thumb


def FINGER_OffsetToAngle(finger_id, offset_to_ref):
    """Calculate finger joint angles from slider offset."""
    pos = METER_TO_MILLIMETER(offset_to_ref)
    finger_id -= 1  # Adjust for array indexing
    
    Angle_Finger = [[0] * MAX_ANGLES_URDF for _ in range(MAX_ANGLES_URDF)]
    
    X_A0 = FingerParams[finger_id][0]
    L_AP = FingerParams[finger_id][1]
    L_AC = FingerParams[finger_id][2]
    L_OC = FingerParams[finger_id][3]
    Angle_BOQ = FingerParams[finger_id][4]
    Angle_COD = FingerParams[finger_id][5]
    Angle_EDF = FingerParams[finger_id][6]
    L_OD = FingerParams[finger_id][7]
    L_OB = FingerParams[finger_id][9]
    L_DE = FingerParams[finger_id][10]
    L_BE = FingerParams[finger_id][11]

    L_OP = X_A0 + pos
    L_OA = SQRT(L_OP * L_OP + L_AP * L_AP)
    Angle_AOP = ANGLE_COS(L_AP, L_OA, L_OP)
    Angle_OAP = HALF_PI - Angle_AOP
    Angle_CAO = ANGLE_COS(L_OC, L_AC, L_OA)
    Angle_CAR = ONE_HALF_PI - Angle_OAP - Angle_CAO

    Angle_COA = ANGLE_COS(L_AC, L_OC, L_OA)
    Angle_COP = Angle_AOP + Angle_COA
    Angle_DOP = Angle_COD + Angle_COP

    Angle_DOQ = PI - Angle_DOP
    Angle_DOB = Angle_BOQ + Angle_DOQ
    L_BD = LENGTH_COS(Angle_DOB, L_OB, L_OD)
    Angle_BDO = ANGLE_COS(L_OB, L_BD, L_OD)
    Angle_BDE = ANGLE_COS(L_BE, L_DE, L_BD)
    Angle_EDO = Angle_BDE - Angle_BDO
    L_OE = LENGTH_COS(Angle_BDE, L_DE, L_OD)
    Angle_EBO = ANGLE_COS(L_OE, L_BE, L_OB)

    Angle_FDO = Angle_EDF + Angle_EDO

    Angle_Finger[finger_id][0] = Angle_CAR - OffsetAngleForURDF[finger_id][0]  # slider_abpart
    Angle_Finger[finger_id][1] = Angle_DOP - OffsetAngleForURDF[finger_id][1]  # proximal
    Angle_Finger[finger_id][2] = Angle_FDO - OffsetAngleForURDF[finger_id][2]  # distal
    Angle_Finger[finger_id][3] = Angle_EBO - OffsetAngleForURDF[finger_id][3]  # connecting

    return Angle_Finger[finger_id]


def HAND_FingerPosToAngle(finger_id, pos):
    """Calculate joint angles for a finger based on position."""
    if finger_id >= NUM_FINGERS + EXTRA_MOTORS:
        return None
    elif finger_id >= NUM_FINGERS:
        return pos
    else:
        if finger_id == 0:
            return THUMB_OffsetToAngle(pos)
        else:
            return FINGER_OffsetToAngle(finger_id, pos)


class RoHandJointLinkage:
    """Joint linkage manager for RoHand right hand robot."""
    
    def __init__(self, prim_path: str, debug: bool = False):
        """Initialize the joint linkage manager.
        
        Args:
            prim_path: The prim path to the robot in the USD stage.
            debug: Whether to enable debug output.
        """
        self.prim_path = prim_path
        self.debug = debug
        self._joint_path_cache = {}
        self._build_joint_path_cache()
    
    def _build_joint_path_cache(self):
        """Build cache of joint paths."""
        stage = get_current_stage()
        if not stage:
            return
            
        # Collect all joint names
        all_joint_names = set()
        for finger_joints in JOINTS_NAME:
            for joint_name in finger_joints:
                all_joint_names.add(joint_name)
        
        # Find each joint and build cache
        for joint_name in all_joint_names:
            joint_path = self._find_joint_path(joint_name)
            if joint_path:
                self._joint_path_cache[joint_name] = joint_path
                if self.debug:
                    print(f"[RoHandJointLinkage] Found joint: {joint_name} -> {joint_path}")
    
    def _find_joint_path(self, joint_name: str) -> Optional[str]:
        """Find the full path of a joint by name."""
        stage = get_current_stage()
        if not stage:
            return None
            
        # Search under the robot prim path
        robot_prim = stage.GetPrimAtPath(self.prim_path)
        if not robot_prim.IsValid():
            return None
            
        # Recursively search for the joint
        return self._recursive_find_joint(robot_prim, joint_name)
    
    def _recursive_find_joint(self, parent_prim, joint_name: str, max_depth: int = 5, current_depth: int = 0) -> Optional[str]:
        """Recursively find a joint by name."""
        if current_depth > max_depth:
            return None
            
        for child_prim in parent_prim.GetChildren():
            if child_prim.GetName() == joint_name:
                # Check if it's a joint type
                type_name = child_prim.GetTypeName()
                if "Joint" in type_name:
                    return str(child_prim.GetPath())
            
            if current_depth < max_depth:
                found_path = self._recursive_find_joint(child_prim, joint_name, max_depth, current_depth + 1)
                if found_path:
                    return found_path
        
        return None
    
    def update_joint_linkage(self, joint_positions: Dict[str, float]):
        """Update joint linkage based on slider positions.
        
        Args:
            joint_positions: Dictionary mapping joint names to their current positions.
        """
        stage = get_current_stage()
        if not stage:
            return
            
        for finger_id in range(NUM_FINGERS):
            try:
                joint_names = JOINTS_NAME[finger_id]
                
                # Determine slider joint
                if finger_id == THUMB_ID:
                    slider_joint_name = joint_names[1]  # th_slider_link
                    slider_joint_index = 1
                else:
                    slider_joint_name = joint_names[0]  # *f_slider_link
                    slider_joint_index = 0
                
                # Get slider position
                if slider_joint_name not in joint_positions:
                    continue
                    
                current_position = joint_positions[slider_joint_name]
                
                # Clamp position
                if finger_id == THUMB_ID:
                    clamped_position = CLAMP(current_position, -0.003, 0.008)
                else:
                    clamped_position = CLAMP(current_position, -0.003, 0.016)
                
                # Calculate target angles
                target_angles = HAND_FingerPosToAngle(finger_id, clamped_position)
                if not target_angles:
                    continue
                
                # Adjust angles based on finger type
                if finger_id == THUMB_ID:
                    target_angles = [target_angles[0], 0.0, target_angles[2]]
                else:
                    target_angles = [0.0, target_angles[1], target_angles[2]]
                
                # Set joint positions
                for joint_idx, joint_name in enumerate(joint_names):
                    if joint_idx == slider_joint_index:
                        continue
                        
                    joint_path = self._joint_path_cache.get(joint_name)
                    if not joint_path:
                        continue
                        
                    joint_prim = stage.GetPrimAtPath(joint_path)
                    if not joint_prim.IsValid():
                        continue
                        
                    target_angle = target_angles[joint_idx]
                    self._set_joint_position(joint_prim, target_angle)
                    
            except Exception as e:
                if self.debug:
                    print(f"[RoHandJointLinkage] Error processing finger {finger_id}: {e}")
    
    def _set_joint_position(self, joint_prim, target_angle: float):
        """Set joint position."""
        try:
            # Convert to degrees for revolute joints
            target_value = math.degrees(target_angle)
            
            # Set drive target
            attr_name = "drive:angular:physics:targetPosition"
            drive_attr = joint_prim.GetAttribute(attr_name)
            if not drive_attr:
                drive_attr = joint_prim.CreateAttribute(attr_name, Sdf.ValueTypeNames.Float)
            
            if drive_attr:
                drive_attr.Set(float(target_value))
                
        except Exception as e:
            if self.debug:
                print(f"[RoHandJointLinkage] Error setting joint position: {e}")
