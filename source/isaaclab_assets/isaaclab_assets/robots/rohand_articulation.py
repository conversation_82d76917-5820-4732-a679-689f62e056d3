# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Custom articulation class for RoHand right hand robot with joint linkage."""

import torch
from typing import Dict, Optional

from isaaclab.assets.articulation import Articulation
from isaaclab.assets.articulation.articulation_data import ArticulationData
from .rohand_joint_linkage import HAND_FingerPosToAngle, CLAMP, JOINTS_NAME, NUM_FINGERS, THUMB_ID


class RoHandArticulation(Articulation):
    """Custom articulation class for RoHand with automatic joint linkage.
    
    This class extends the base Articulation class to automatically handle
    the joint linkage calculations for the RoHand robot fingers.
    """
    
    def __init__(self, cfg):
        """Initialize the RoHand articulation.
        
        Args:
            cfg: The articulation configuration.
        """
        super().__init__(cfg)
        self._joint_linkage_managers = {}
        self._slider_joint_indices = {}
        self._target_joint_indices = {}
        self._last_slider_positions = {}
        
    def _initialize_impl(self):
        """Initialize the articulation implementation."""
        super()._initialize_impl()
        
        # Initialize joint linkage managers for each environment
        for env_idx in range(self.num_instances):
            prim_path = f"{self.cfg.prim_path.replace('.*', str(env_idx))}"
            self._joint_linkage_managers[env_idx] = RoHandJointLinkage(prim_path, debug=False)
            
        # Find slider and target joint indices
        self._find_joint_indices()
        
        # Initialize last positions
        for env_idx in range(self.num_instances):
            self._last_slider_positions[env_idx] = {}
    
    def _find_joint_indices(self):
        """Find the indices of slider and target joints."""
        joint_names = self.joint_names
        
        for env_idx in range(self.num_instances):
            self._slider_joint_indices[env_idx] = {}
            self._target_joint_indices[env_idx] = {}
            
            for finger_id in range(NUM_FINGERS):
                finger_joint_names = JOINTS_NAME[finger_id]
                
                # Find slider joint
                if finger_id == THUMB_ID:
                    slider_name = finger_joint_names[1]  # th_slider_link
                else:
                    slider_name = finger_joint_names[0]  # *f_slider_link
                
                # Find slider joint index
                for i, joint_name in enumerate(joint_names):
                    if slider_name in joint_name:
                        self._slider_joint_indices[env_idx][finger_id] = i
                        break
                
                # Find target joint indices
                target_joints = []
                for joint_name in finger_joint_names:
                    if joint_name != slider_name:
                        for i, jn in enumerate(joint_names):
                            if joint_name in jn:
                                target_joints.append(i)
                                break
                
                self._target_joint_indices[env_idx][finger_id] = target_joints
    
    def write_data_to_sim(self):
        """Write data to simulation and update joint linkage."""
        # First write the normal data
        super().write_data_to_sim()
        
        # Then update joint linkage
        self._update_joint_linkage()
    
    def _update_joint_linkage(self):
        """Update joint linkage based on current slider positions."""
        if not hasattr(self, '_joint_linkage_managers'):
            return
            
        # Get current joint positions
        current_positions = self.data.joint_pos.clone()
        
        for env_idx in range(self.num_instances):
            if env_idx not in self._joint_linkage_managers:
                continue
                
            linkage_manager = self._joint_linkage_managers[env_idx]
            slider_indices = self._slider_joint_indices.get(env_idx, {})
            target_indices = self._target_joint_indices.get(env_idx, {})
            
            # Check if any slider positions have changed significantly
            position_changed = False
            current_slider_positions = {}
            
            for finger_id, slider_idx in slider_indices.items():
                if slider_idx < current_positions.shape[1]:
                    current_pos = current_positions[env_idx, slider_idx].item()
                    current_slider_positions[finger_id] = current_pos
                    
                    # Check if position changed
                    last_pos = self._last_slider_positions[env_idx].get(finger_id, 0.0)
                    if abs(current_pos - last_pos) > 1e-6:  # Small threshold for numerical precision
                        position_changed = True
                        self._last_slider_positions[env_idx][finger_id] = current_pos
            
            # Only update if positions changed
            if position_changed:
                # Create joint positions dictionary for the linkage manager
                joint_positions = {}
                for finger_id in range(NUM_FINGERS):
                    finger_joint_names = JOINTS_NAME[finger_id]
                    
                    # Get slider joint name and position
                    if finger_id == THUMB_ID:
                        slider_name = finger_joint_names[1]
                    else:
                        slider_name = finger_joint_names[0]
                    
                    if finger_id in current_slider_positions:
                        joint_positions[slider_name] = current_slider_positions[finger_id]
                
                # Update linkage (this will set USD joint positions directly)
                linkage_manager.update_joint_linkage(joint_positions)
    
    def reset(self, env_ids: Optional[torch.Tensor] = None):
        """Reset the articulation state."""
        super().reset(env_ids)
        
        # Reset last slider positions for the specified environments
        if env_ids is None:
            env_ids = torch.arange(self.num_instances, device=self.device)
        
        for env_idx in env_ids.tolist():
            if env_idx in self._last_slider_positions:
                self._last_slider_positions[env_idx].clear()


class RoHandArticulationData(ArticulationData):
    """Data container for RoHand articulation with joint linkage support."""
    
    def __init__(self, root_physx_view, device: str):
        """Initialize the RoHand articulation data.
        
        Args:
            root_physx_view: The root physics view.
            device: The device to use for computations.
        """
        super().__init__(root_physx_view, device)
        
        # Additional data for joint linkage
        self.slider_joint_pos = None
        self.target_joint_pos = None
    
    def update(self, dt: float):
        """Update the articulation data.
        
        Args:
            dt: The time step.
        """
        super().update(dt)
        
        # Update slider and target joint positions if needed
        # This can be used for monitoring or additional processing
        pass
