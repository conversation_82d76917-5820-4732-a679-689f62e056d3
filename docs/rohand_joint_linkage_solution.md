# RoHand Joint Linkage Solution for IsaacLab

## Problem Description

The USD file `/home/<USER>/IsaacLab/usd_files/wheelbot-righthand-change.usd` contains Python scripting (BehaviorScript) that works when running Isaac Sim standalone, but fails to execute during IsaacLab training sessions.

## Root Cause

The issue occurs because:

1. **Extension Loading**: <PERSON><PERSON><PERSON>'s headless mode may not load the `omni.kit.scripting` extension by default
2. **USD Loading Method**: IsaacLab uses different USD loading mechanisms that may not trigger BehaviorScript execution
3. **Kit Configuration**: Isaac<PERSON>ab's Kit configuration files may disable certain scripting features for performance

## Solutions Implemented

### Solution 1: Enable Python Scripting Extension

Modified `source/isaaclab_assets/isaaclab_assets/robots/rohand_right.py` to explicitly enable the scripting extension:

```python
# Enable Python scripting extension for USD behavior scripts
try:
    from omni.isaac.core.utils.extensions import enable_extension
    enable_extension("omni.kit.scripting")
except ImportError:
    print("[WARNING] Could not enable omni.kit.scripting extension. USD Python scripts may not work.")
```

### Solution 2: Custom Joint Linkage Manager

Created `source/isaaclab_assets/isaaclab_assets/robots/rohand_joint_linkage.py` - a Python implementation of the joint linkage logic that was originally in the USD BehaviorScript.

Key features:
- Implements the same mathematical calculations as the original script
- Handles finger joint linkage based on slider positions
- Works directly with IsaacLab's simulation framework

### Solution 3: Custom Articulation Class

Created `source/isaaclab_assets/isaaclab_assets/robots/rohand_articulation.py` - a custom Articulation class that automatically handles joint linkage:

```python
class RoHandArticulation(Articulation):
    """Custom articulation class for RoHand with automatic joint linkage."""
    
    def write_data_to_sim(self):
        """Write data to simulation and update joint linkage."""
        super().write_data_to_sim()
        self._update_joint_linkage()
```

### Solution 4: Updated Robot Configuration

Modified the robot configuration to use the custom articulation class:

```python
ROHAND_RIGHT_CFG = ArticulationCfg(
    class_type=RoHandArticulation,  # Use custom articulation class with joint linkage
    # ... rest of configuration
)
```

## Testing

Created `scripts/test_rohand_joint_linkage.py` to verify the joint linkage functionality:

```bash
./isaaclab.sh -p scripts/test_rohand_joint_linkage.py --num_envs 1
```

## Alternative Solutions

If the above solutions don't work completely, consider these alternatives:

### Alternative 1: Manual USD Loading

Use `omni.usd.get_context().open_stage()` instead of the standard IsaacLab USD loading:

```python
import omni.usd
from omni.isaac.core.utils.stage import is_stage_loading

# Load USD with proper stage context
omni.usd.get_context().open_stage(usd_path)

# Wait for loading to complete
while is_stage_loading():
    simulation_app.update()
```

### Alternative 2: Kit Configuration Modification

Modify the Kit configuration files in `apps/` to ensure scripting extensions are loaded:

```toml
[dependencies]
"omni.kit.scripting" = {}

[settings]
# Enable scripting features
app.scripting.enabled = true
```

### Alternative 3: Environment Variable

Set environment variables to force extension loading:

```bash
export ISAAC_SIM_ENABLE_SCRIPTING=1
./isaaclab.sh -p your_training_script.py
```

## Recommended Approach

The recommended approach is to use **Solution 3** (Custom Articulation Class) because:

1. It's more reliable than depending on USD BehaviorScript execution
2. It integrates seamlessly with IsaacLab's architecture
3. It provides better performance and debugging capabilities
4. It's easier to maintain and modify

## Usage

After implementing the solutions, your training scripts should work normally with the joint linkage functionality automatically handled by the custom articulation class.

## Troubleshooting

If issues persist:

1. Check that the `omni.kit.scripting` extension is available in your Isaac Sim installation
2. Verify that the USD file paths are correct
3. Enable debug output in the joint linkage manager
4. Test with the provided test script first before running full training

## Notes

- The joint linkage calculations are based on the original BehaviorScript implementation
- All mathematical formulas and parameters have been preserved
- The solution is backward compatible with existing training scripts
